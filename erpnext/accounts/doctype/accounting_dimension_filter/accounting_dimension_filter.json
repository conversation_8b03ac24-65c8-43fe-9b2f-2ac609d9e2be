{"actions": [], "autoname": "format:{accounting_dimension}-{#####}", "creation": "2020-11-08 18:28:11.906146", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["accounting_dimension", "fieldname", "disabled", "column_break_2", "company", "apply_restriction_on_values", "allow_or_restrict", "section_break_4", "accounts", "column_break_6", "dimensions", "section_break_10", "dimension_filter_help"], "fields": [{"fieldname": "accounting_dimension", "fieldtype": "Select", "in_list_view": 1, "label": "Accounting Dimension", "reqd": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "section_break_4", "fieldtype": "Section Break", "hide_border": 1}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"depends_on": "eval:doc.apply_restriction_on_values == 1;", "fieldname": "allow_or_restrict", "fieldtype": "Select", "label": "Allow Or Restrict Dimension", "options": "Allow\nRestrict", "reqd": 1}, {"fieldname": "accounts", "fieldtype": "Table", "label": "Applicable On Account", "options": "Applicable On Account", "reqd": 1}, {"depends_on": "eval:doc.accounting_dimension && doc.apply_restriction_on_values", "fieldname": "dimensions", "fieldtype": "Table", "label": "Applicable Dimension", "mandatory_depends_on": "eval:doc.apply_restriction_on_values == 1;", "options": "Allowed Dimension"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1}, {"fieldname": "dimension_filter_help", "fieldtype": "HTML", "label": "Dimension Filter Help"}, {"fieldname": "section_break_10", "fieldtype": "Section Break"}, {"default": "1", "fieldname": "apply_restriction_on_values", "fieldtype": "Check", "label": "Apply restriction on dimension values"}, {"fieldname": "fieldname", "fieldtype": "Data", "hidden": 1, "label": "Fieldname"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-08-08 14:13:22.203011", "modified_by": "Administrator", "module": "Accounts", "name": "Accounting Dimension Filter", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}], "quick_entry": 1, "sort_field": "creation", "sort_order": "DESC", "states": [], "track_changes": 1}