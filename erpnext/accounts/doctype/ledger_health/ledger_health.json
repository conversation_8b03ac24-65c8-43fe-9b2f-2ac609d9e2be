{"actions": [], "autoname": "autoincrement", "creation": "2024-03-26 17:01:47.443986", "doctype": "DocType", "engine": "InnoDB", "field_order": ["voucher_type", "voucher_no", "checked_on", "debit_credit_mismatch", "general_and_payment_ledger_mismatch"], "fields": [{"fieldname": "voucher_type", "fieldtype": "Data", "label": "Voucher Type"}, {"fieldname": "voucher_no", "fieldtype": "Data", "label": "Voucher No"}, {"default": "0", "fieldname": "debit_credit_mismatch", "fieldtype": "Check", "label": "Debit-Credit mismatch"}, {"fieldname": "checked_on", "fieldtype": "Datetime", "label": "Checked On"}, {"default": "0", "fieldname": "general_and_payment_ledger_mismatch", "fieldtype": "Check", "label": "General and Payment Ledger mismatch"}], "in_create": 1, "index_web_pages_for_search": 1, "links": [], "modified": "2024-04-09 11:16:07.044484", "modified_by": "Administrator", "module": "Accounts", "name": "Ledger Health", "naming_rule": "Autoincrement", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "read_only": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}