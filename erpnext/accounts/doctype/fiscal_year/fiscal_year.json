{"actions": [], "allow_import": 1, "autoname": "field:year", "creation": "2013-01-22 16:50:25", "description": "Represents a Financial Year. All accounting entries and other major transactions are tracked against the Fiscal Year.", "doctype": "DocType", "document_type": "Setup", "engine": "InnoDB", "field_order": ["year", "disabled", "is_short_year", "year_start_date", "year_end_date", "companies", "auto_created"], "fields": [{"description": "For e.g. 2012, 2012-13", "fieldname": "year", "fieldtype": "Data", "in_list_view": 1, "label": "Year Name", "oldfieldname": "year", "oldfieldtype": "Data", "reqd": 1, "unique": 1}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "year_start_date", "fieldtype": "Date", "in_list_view": 1, "label": "Year Start Date", "no_copy": 1, "oldfieldname": "year_start_date", "oldfieldtype": "Date", "reqd": 1, "set_only_once": 1}, {"fieldname": "year_end_date", "fieldtype": "Date", "in_list_view": 1, "label": "Year End Date", "no_copy": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "companies", "fieldtype": "Table", "label": "Companies", "options": "Fiscal Year Company"}, {"default": "0", "fieldname": "auto_created", "fieldtype": "Check", "hidden": 1, "label": "Auto Created", "no_copy": 1, "print_hide": 1, "read_only": 1}, {"default": "0", "description": "More/Less than 12 months.", "fieldname": "is_short_year", "fieldtype": "Check", "label": "Is Short/Long Year", "set_only_once": 1}], "icon": "fa fa-calendar", "idx": 1, "links": [], "modified": "2024-05-27 17:29:55.560840", "modified_by": "Administrator", "module": "Accounts", "name": "Fiscal Year", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"read": 1, "role": "Sales User"}, {"read": 1, "role": "Purchase User"}, {"read": 1, "role": "Accounts User"}, {"read": 1, "role": "Stock User"}, {"read": 1, "role": "Employee"}, {"read": 1, "role": "Accounts Manager"}, {"read": 1, "role": "Stock Manager"}, {"read": 1, "role": "Auditor"}], "show_name_in_global_search": 1, "sort_field": "name", "sort_order": "DESC", "states": []}