{"actions": [], "autoname": "field:template_title", "creation": "2020-04-09 01:32:51.332301", "doctype": "DocType", "document_type": "Document", "editable_grid": 1, "engine": "InnoDB", "field_order": ["section_break_1", "template_title", "voucher_type", "naming_series", "column_break_3", "company", "is_opening", "multi_currency", "section_break_3", "accounts"], "fields": [{"fieldname": "section_break_1", "fieldtype": "Section Break"}, {"fieldname": "voucher_type", "fieldtype": "Select", "in_list_view": 1, "label": "Journal Entry Type", "options": "Journal Entry\nInter Company Journal Entry\nBank Entry\nCash Entry\nCredit Card Entry\nDebit Note\nCredit Note\nContra Entry\nExcise Entry\nWrite Off Entry\nOpening Entry\nDepreciation Entry\nExchange Rate Revaluation", "reqd": 1}, {"fieldname": "company", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Company", "options": "Company", "remember_last_selected_value": 1, "reqd": 1}, {"fieldname": "column_break_3", "fieldtype": "Column Break"}, {"default": "No", "fieldname": "is_opening", "fieldtype": "Select", "label": "Is Opening", "options": "No\nYes"}, {"fieldname": "accounts", "fieldtype": "Table", "label": "Accounting Entries", "options": "Journal Entry Template Account"}, {"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "print_hide": 1, "reqd": 1, "set_only_once": 1}, {"fieldname": "template_title", "fieldtype": "Data", "label": "Template Title", "reqd": 1, "unique": 1}, {"fieldname": "section_break_3", "fieldtype": "Section Break"}, {"default": "0", "fieldname": "multi_currency", "fieldtype": "Check", "label": "Multi Currency"}], "links": [], "modified": "2020-05-01 18:32:01.420488", "modified_by": "Administrator", "module": "Accounts", "name": "Journal Entry Template", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Auditor", "share": 1}], "search_fields": "voucher_type, company", "sort_field": "modified", "sort_order": "DESC", "title_field": "template_title", "track_changes": 1}