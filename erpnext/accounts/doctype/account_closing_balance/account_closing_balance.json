{"actions": [], "creation": "2023-02-21 15:20:59.586811", "default_view": "List", "doctype": "DocType", "document_type": "Document", "engine": "InnoDB", "field_order": ["closing_date", "account", "cost_center", "debit", "credit", "account_currency", "debit_in_account_currency", "credit_in_account_currency", "project", "company", "finance_book", "period_closing_voucher", "is_period_closing_voucher_entry"], "fields": [{"fieldname": "closing_date", "fieldtype": "Date", "in_filter": 1, "in_list_view": 1, "label": "Closing Date", "oldfieldname": "posting_date", "oldfieldtype": "Date", "search_index": 1}, {"fieldname": "account", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Account", "oldfieldname": "account", "oldfieldtype": "Link", "options": "Account", "search_index": 1}, {"fieldname": "cost_center", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "label": "Cost Center", "oldfieldname": "cost_center", "oldfieldtype": "Link", "options": "Cost Center"}, {"fieldname": "debit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Debit Amount", "oldfieldname": "debit", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency"}, {"fieldname": "credit", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Credit Amount", "oldfieldname": "credit", "oldfieldtype": "<PERSON><PERSON><PERSON><PERSON>", "options": "Company:company:default_currency"}, {"fieldname": "account_currency", "fieldtype": "Link", "label": "Account <PERSON><PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "debit_in_account_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Debit Amount in Account Currency", "options": "account_currency"}, {"fieldname": "credit_in_account_currency", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Credit Amount in Account Currency", "options": "account_currency"}, {"fieldname": "project", "fieldtype": "Link", "label": "Project", "options": "Project"}, {"fieldname": "company", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "search_index": 1}, {"fieldname": "finance_book", "fieldtype": "Link", "label": "Finance Book", "options": "Finance Book"}, {"fieldname": "period_closing_voucher", "fieldtype": "Link", "in_standard_filter": 1, "label": "Period Closing Voucher", "options": "Period Closing Voucher", "search_index": 1}, {"default": "0", "fieldname": "is_period_closing_voucher_entry", "fieldtype": "Check", "label": "Is Period Closing Voucher Entry"}], "icon": "fa fa-list", "in_create": 1, "links": [], "modified": "2023-03-06 08:56:36.393237", "modified_by": "Administrator", "module": "Accounts", "name": "Account Closing Balance", "owner": "Administrator", "permissions": [{"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts User"}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Accounts Manager"}, {"export": 1, "read": 1, "report": 1, "role": "Auditor"}], "sort_field": "modified", "sort_order": "DESC", "states": []}