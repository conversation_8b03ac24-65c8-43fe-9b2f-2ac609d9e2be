{"actions": [], "allow_rename": 1, "creation": "2023-09-15 21:28:28.054773", "default_view": "List", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["section_break_cvfg", "company", "column_break_hcam", "from_date", "column_break_qxbi", "to_date", "column_break_iwny", "algorithm", "section_break_8ph9", "current_node", "section_break_ngid", "bisect_heatmap", "section_break_hmsy", "bisecting_from", "current_from_date", "column_break_uqyd", "bisecting_to", "current_to_date", "section_break_hbyo", "heading_cppb", "p_l_summary", "column_break_aivo", "balance_sheet_summary", "b_s_summary", "column_break_gvwx", "difference_heading", "difference"], "fields": [{"fieldname": "column_break_qxbi", "fieldtype": "Column Break"}, {"fieldname": "from_date", "fieldtype": "Datetime", "label": "From Date"}, {"fieldname": "to_date", "fieldtype": "Datetime", "label": "To Date"}, {"default": "BFS", "fieldname": "algorithm", "fieldtype": "Select", "label": "Algorithm", "options": "BFS\nDFS"}, {"fieldname": "column_break_iwny", "fieldtype": "Column Break"}, {"fieldname": "current_node", "fieldtype": "Link", "label": "Current Node", "options": "Bisect Nodes"}, {"fieldname": "section_break_hmsy", "fieldtype": "Section Break"}, {"fieldname": "current_from_date", "fieldtype": "Datetime", "read_only": 1}, {"fieldname": "current_to_date", "fieldtype": "Datetime", "read_only": 1}, {"fieldname": "column_break_uqyd", "fieldtype": "Column Break"}, {"fieldname": "section_break_hbyo", "fieldtype": "Section Break"}, {"fieldname": "p_l_summary", "fieldtype": "Float", "read_only": 1}, {"fieldname": "b_s_summary", "fieldtype": "Float", "read_only": 1}, {"fieldname": "difference", "fieldtype": "Float", "read_only": 1}, {"fieldname": "column_break_aivo", "fieldtype": "Column Break"}, {"fieldname": "column_break_gvwx", "fieldtype": "Column Break"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company"}, {"fieldname": "column_break_hcam", "fieldtype": "Column Break"}, {"fieldname": "section_break_ngid", "fieldtype": "Section Break"}, {"fieldname": "section_break_8ph9", "fieldtype": "Section Break", "hidden": 1}, {"fieldname": "bisect_heatmap", "fieldtype": "HTML", "label": "Heatmap"}, {"fieldname": "heading_cppb", "fieldtype": "Heading", "label": "Profit and Loss Summary"}, {"fieldname": "balance_sheet_summary", "fieldtype": "Heading", "label": "Balance Sheet Summary"}, {"fieldname": "difference_heading", "fieldtype": "Heading", "label": "Difference"}, {"fieldname": "bisecting_from", "fieldtype": "Heading", "label": "Bisecting From"}, {"fieldname": "bisecting_to", "fieldtype": "Heading", "label": "Bisecting To"}, {"fieldname": "section_break_cvfg", "fieldtype": "Section Break"}], "hide_toolbar": 1, "index_web_pages_for_search": 1, "issingle": 1, "links": [], "modified": "2023-12-01 16:49:54.073890", "modified_by": "Administrator", "module": "Accounts", "name": "Bisect Accounting Statements", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "print": 1, "read": 1, "role": "Administrator", "share": 1, "write": 1}], "read_only": 1, "sort_field": "modified", "sort_order": "DESC", "states": []}